import React from 'react';
import {
  ArrowRightIcon,
  PlayIcon,
  StarIcon
} from '@heroicons/react/24/outline';

const Hero: React.FC = () => {
  return (
    <section id="hero" className="h-screen flex items-center justify-center relative overflow-hidden">
      {/* Video Background with 2% Blur */}
      <video
        autoPlay
        loop
        muted
        playsInline
        className="absolute inset-0 w-full h-full object-cover"
        style={{ filter: 'blur(2px)' }}
      >
        <source src="/video.mp4" type="video/mp4" />
      </video>

      {/* Overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-900/30 via-white/70 to-indigo-900/30"></div>

      {/* Content */}
      <div className="w-full max-w-5xl mx-auto px-6 sm:px-8 lg:px-12 xl:px-16 relative z-10">
        <div className="flex flex-col items-center text-center space-y-6">
          {/* Badge */}
          <div className="inline-flex items-center justify-center px-4 py-2 bg-blue-100/90 backdrop-blur-sm rounded-full text-blue-700 text-sm font-medium tracking-wide">
            <StarIcon className="w-4 h-4 mr-2 flex-shrink-0" />
            <span>Plateforme N°1 en Algérie</span>
          </div>

          {/* Main Heading */}
          <div className="w-full max-w-4xl mx-auto">
            <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold text-gray-900 leading-tight tracking-tight">
              Avec <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">Sharyou</span> créez votre boutique rapidement
            </h1>
            <h2 className="mt-3 text-lg sm:text-xl font-medium text-blue-700/90">La seule plateforme e-commerce IA pensée pour l'Algérie</h2>
          </div>

          {/* Description */}
          <div className="w-full max-w-3xl mx-auto">
            <p className="text-base sm:text-lg text-gray-700 leading-relaxed">
              La plateforme e-commerce simple et puissante pour les entrepreneurs algériens.
            </p>
          </div>

          {/* Buttons */}
          <div className="flex flex-row items-center justify-center gap-4 w-full max-w-4xl mx-auto mt-2">
            <button className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center group backdrop-blur-sm whitespace-nowrap animate-bounce-slow">
              <span>Créer ma boutique gratuitement</span>
              <ArrowRightIcon className="w-5 h-5 ml-2 flex-shrink-0 group-hover:translate-x-1 transition-transform" />
            </button>
            <button className="flex items-center justify-center px-6 py-3 border-2 border-gray-300 bg-white/90 backdrop-blur-sm rounded-xl text-gray-700 font-semibold hover:bg-gray-50 hover:border-gray-400 transition-all duration-300 whitespace-nowrap">
              <PlayIcon className="w-4 h-4 mr-2 flex-shrink-0" />
              <span>Voir la démonstration</span>
            </button>
          </div>
          {/* Trust Signal */}
          <div className="mt-6 flex flex-col items-center">
            <span className="inline-flex items-center px-4 py-2 bg-white/80 rounded-full shadow text-gray-700 font-medium text-sm">
              <StarIcon className="w-4 h-4 mr-2 text-yellow-400" />
              Déjà plus de 10 000 entrepreneurs nous font confiance
            </span>
          </div>
          {/* Testimonial Snippet */}
          <div className="mt-4 max-w-xl mx-auto text-center">
            <blockquote className="italic text-gray-600 text-base">"Sharyou m'a permis de lancer ma boutique en ligne en quelques minutes, sans aucune compétence technique !"</blockquote>
            <div className="mt-2 text-sm text-gray-500">— Samir B., Oran</div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;

<style jsx>{`
@keyframes bounce-slow {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-8px); }
}
.animate-bounce-slow {
  animation: bounce-slow 2.2s infinite;
}
`}</style>